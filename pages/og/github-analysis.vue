<script setup lang="ts">
// 从查询参数获取数据
const route = useRoute()

// 构建用户数据
const user = computed(() => ({
  name: route.query.userName as string || 'GitHub User',
  avatar: route.query.userAvatar as string || '/image/avator.png',
  role: route.query.userRole as string || 'Developer',
  login: route.query.userLogin as string || '',
  bio: route.query.userBio as string || '',
}))

// 构建统计数据
const stats = computed(() => ({
  repositories: parseInt(route.query.repositories as string) || 0,
  stars: parseInt(route.query.stars as string) || 0,
  pullRequests: parseInt(route.query.pullRequests as string) || 0,
}))

// 构建角色模型数据
const roleModel = computed(() => {
  if (!route.query.roleModelName) return null
  return {
    name: route.query.roleModelName as string,
    avatar: route.query.roleModelAvatar as string || '/image/avator.png',
    title: route.query.roleModelTitle as string || '',
    achievement: route.query.roleModelAchievement as string || '',
  }
})

// 解析编程语言数据
const languages = computed(() => {
  try {
    return route.query.languages ? JSON.parse(route.query.languages as string) : {}
  } catch {
    return {}
  }
})

const languageTotal = computed(() => parseInt(route.query.languageTotal as string) || 0)
const income = computed(() => parseInt(route.query.income as string) || 0)
const additions = computed(() => parseInt(route.query.additions as string) || 0)
const deletions = computed(() => parseInt(route.query.deletions as string) || 0)
const workExperience = computed(() => parseInt(route.query.workExperience as string) || 0)

// 格式化收入显示
const formatIncome = (income: number) => {
  if (income >= 1000000) {
    return `${(income / 1000000).toFixed(1)}M`
  } else if (income >= 1000) {
    return `${(income / 1000).toFixed(0)}K`
  }
  return income.toString()
}

// 使用 defineOgImageComponent 来定义OG图片
defineOgImageComponent('OgImageGitHubAnalysis', {
  // 从查询参数传递数据
  userName: route.query.userName as string || 'GitHub User',
  userAvatar: route.query.userAvatar as string || '/image/avator.png',
  userLogin: route.query.userLogin as string || '',
  userBio: route.query.userBio as string || '',
  userRole: route.query.userRole as string || 'Developer',

  repositories: parseInt(route.query.repositories as string) || 0,
  stars: parseInt(route.query.stars as string) || 0,
  pullRequests: parseInt(route.query.pullRequests as string) || 0,
  additions: parseInt(route.query.additions as string) || 0,
  deletions: parseInt(route.query.deletions as string) || 0,
  workExperience: parseInt(route.query.workExperience as string) || 0,

  income: parseInt(route.query.income as string) || 0,

  roleModelName: route.query.roleModelName as string || '',
  roleModelAvatar: route.query.roleModelAvatar as string || '',
  roleModelTitle: route.query.roleModelTitle as string || '',
  roleModelAchievement: route.query.roleModelAchievement as string || '',

  languages: route.query.languages as string || '{}',
  languageTotal: parseInt(route.query.languageTotal as string) || 0,
})

// 禁用页面的默认布局和导航
definePageMeta({
  layout: false,
})

// 设置页面头部
useHead({
  title: 'GitHub Analysis OG Image',
  meta: [
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})
</script>

<template>
  <div class="w-[1200px] h-[630px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] p-6 relative overflow-hidden">
    <!-- 背景图案 -->
    <div class="absolute inset-0 bg-[url(/image/graphbg.png)] bg-right bg-contain bg-no-repeat opacity-30"></div>

    <div class="relative w-[850px]" style="z-index: 10;">
      <!-- 顶部用户信息 -->
      <div class="flex items-center justify-start h-[80px]" v-if="user">
        <img :src="user.avatar" class="w-20 h-20 rounded-full mr-4 border-2 border-white shadow-lg" />
        <div class="flex flex-col flex-1 justify-around">
          <div class="flex items-center gap-4">
            <h2 class="text-2xl font-bold text-gray-900">{{ user.name }}</h2>
            <div class="flex items-start gap-1 text-sm text-gray-600 flex-1">
              <span class="line-clamp-2">{{ user.bio || user.role }}</span>
            </div>
          </div>
          <div class="flex items-center" v-if="user?.login">
            <span class="text-blue-600 text-sm font-medium">
              github.com/{{ user.login }}
            </span>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-between mt-6 gap-6">
        <!-- 左侧：Overview 和语言分布 -->
        <div class="flex flex-col justify-between p-6 flex-1 h-[450px] bg-white/80 rounded-2xl shadow-xl border border-gray-200">
          <div>
            <div class="flex items-center font-bold text-lg mb-4 text-gray-900">
              <span class="mr-2">📊</span>
              Overview
            </div>

            <!-- 统计数据 -->
            <div class="grid grid-cols-2 gap-4 mb-6">
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ stats.repositories }}</div>
                <div class="text-sm text-gray-600">Repositories</div>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-yellow-600">{{ stats.stars }}</div>
                <div class="text-sm text-gray-600">Stars</div>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">{{ additions }}</div>
                <div class="text-sm text-gray-600">Additions</div>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-red-600">{{ deletions }}</div>
                <div class="text-sm text-gray-600">Deletions</div>
              </div>
            </div>

            <!-- 编程语言 -->
            <div v-if="languages && Object.keys(languages).length > 0">
              <div class="text-sm font-semibold mb-3 text-gray-900">Top Languages</div>
              <div class="space-y-2">
                <div
                  v-for="([lang, count], index) in Object.entries(languages).slice(0, 4)"
                  :key="lang"
                  class="flex items-center justify-between"
                >
                  <span class="text-sm text-gray-700">{{ lang }}</span>
                  <span class="text-sm font-medium text-gray-900">{{ ((count / languageTotal) * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：角色模型和薪资信息 -->
        <div class="flex flex-col gap-4 w-[350px]">
          <!-- 角色模型 -->
          <div v-if="roleModel" class="p-6 bg-white/80 rounded-2xl shadow-xl border border-gray-200">
            <div class="flex items-center font-bold text-lg mb-4 text-gray-900">
              <span class="mr-2">🎯</span>
              Role Model
            </div>
            <div class="flex items-center gap-4">
              <img :src="roleModel.avatar" class="w-16 h-16 rounded-full border-2 border-gray-200" />
              <div>
                <div class="font-semibold text-gray-900">{{ roleModel.name }}</div>
                <div class="text-sm text-gray-600">{{ roleModel.title }}</div>
                <div class="text-xs text-blue-600 mt-1">{{ roleModel.achievement }}</div>
              </div>
            </div>
          </div>

          <!-- 薪资估算 -->
          <div v-if="income" class="p-6 bg-white/80 rounded-2xl shadow-xl border border-gray-200">
            <div class="flex items-center font-bold text-lg mb-4 text-gray-900">
              <span class="mr-2">💰</span>
              Estimated Income
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600">
                ${{ formatIncome(income) }}
              </div>
              <div class="text-sm text-gray-600 mt-1">Annual Salary</div>
            </div>
          </div>

          <!-- 工作经验 -->
          <div v-if="workExperience" class="p-6 bg-white/80 rounded-2xl shadow-xl border border-gray-200">
            <div class="flex items-center font-bold text-lg mb-4 text-gray-900">
              <span class="mr-2">⏱️</span>
              Experience
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600">
                {{ workExperience }}
              </div>
              <div class="text-sm text-gray-600 mt-1">Years</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部品牌信息 -->
      <div class="absolute bottom-4 right-6 flex items-center gap-3">
        <div class="text-sm text-gray-500">
          Powered by DINQ.io
        </div>
        <img src="/image/qrcode.png" alt="QR Code" class="w-12 h-12" />
      </div>
    </div>
  </div>
</template>



<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 确保页面适合OG图片尺寸 */
html, body {
  margin: 0;
  padding: 0;
  width: 1200px;
  height: 630px;
  overflow: hidden;
}
</style>
