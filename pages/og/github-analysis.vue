<script setup lang="ts">
// 从查询参数获取数据
const route = useRoute()

// 构建用户数据
const user = computed(() => ({
  name: route.query.userName as string || 'GitHub User',
  avatar: route.query.userAvatar as string || '/image/avator.png',
  role: route.query.userRole as string || 'Developer',
  login: route.query.userLogin as string || '',
  bio: route.query.userBio as string || '',
}))

// 构建统计数据
const stats = computed(() => ({
  repositories: parseInt(route.query.repositories as string) || 0,
  stars: parseInt(route.query.stars as string) || 0,
  pullRequests: parseInt(route.query.pullRequests as string) || 0,
}))

// 构建角色模型数据
const roleModel = computed(() => {
  if (!route.query.roleModelName) return null
  return {
    name: route.query.roleModelName as string,
    avatar: route.query.roleModelAvatar as string || '/image/avator.png',
    title: route.query.roleModelTitle as string || '',
    achievement: route.query.roleModelAchievement as string || '',
  }
})

// 解析编程语言数据
const languages = computed(() => {
  try {
    return route.query.languages ? JSON.parse(route.query.languages as string) : {}
  } catch {
    return {}
  }
})

// 构建insightsItems数据（模拟SegmentTable的数据）
const insightsItems = computed(() => [
  { label: 'GitHub Stars', value: stats.value.stars.toLocaleString() },
  { label: 'Work Experience', value: workExperience.value },
  { label: 'Total Issues', value: '0' }, // 暂时使用默认值
  { label: 'Repositories', value: stats.value.repositories.toLocaleString() },
  { label: 'Total PRs', value: stats.value.pullRequests.toLocaleString() },
])

// 构建特色项目数据
const featureProject = computed(() => {
  const projectName = route.query.featureProjectName as string
  if (!projectName) return null
  return {
    name: projectName,
    description: route.query.featureProjectDescription as string || 'A high-performance project',
    stargazerCount: parseInt(route.query.featureProjectStars as string) || 0,
    forkCount: parseInt(route.query.featureProjectForks as string) || 0,
    url: route.query.featureProjectUrl as string || '#',
  }
})

// 构建最有价值PR数据
const mostValuablePR = computed(() => {
  const prTitle = route.query.mostValuablePRTitle as string
  if (!prTitle) return null
  return {
    title: prTitle,
    impact: route.query.mostValuablePRImpact as string || 'Significant improvement',
    repository: route.query.mostValuablePRRepository as string || 'repository',
    url: route.query.mostValuablePRUrl as string || '#',
  }
})

// 构建薪资级别数据
const valuationLevel = computed(() => {
  const salaryRange = route.query.salaryRange as string
  if (!salaryRange) return null
  return {
    salary_range: salaryRange
  }
})

const languageTotal = computed(() => parseInt(route.query.languageTotal as string) || 0)
const income = computed(() => parseInt(route.query.income as string) || 0)
const additions = computed(() => parseInt(route.query.additions as string) || 0)
const deletions = computed(() => parseInt(route.query.deletions as string) || 0)
const workExperience = computed(() => parseInt(route.query.workExperience as string) || 0)

// 格式化数字函数
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(0) + 'k'
  }
  return num.toString()
}

// 格式化薪资显示
const formatMinSalaryWithSuffix = (salaryRange: string): string => {
  if (!salaryRange) return '120K'

  // 提取第一个数字
  const match = salaryRange.match(/\$?(\d+)/)
  if (match) {
    const num = parseInt(match[1])
    if (num >= 1000) {
      return `${Math.floor(num / 1000)}K`
    }
    return num.toString()
  }
  return '120K'
}

// 饼图颜色
const pieColors = ['#7F95CE', '#F8E9C8', '#CB7C5D', '#D2CEC4', '#B89EDA']

// 计算SVG饼图片段
const pieSegments = computed(() => {
  if (!languages.value || !languageTotal.value) return []

  const languageEntries = Object.entries(languages.value)
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 4) // 只取前4种语言

  let currentAngle = 0
  const segments = []
  const centerX = 80
  const centerY = 80
  const radius = 65
  const innerRadius = 25

  for (let i = 0; i < languageEntries.length; i++) {
    const [lang, count] = languageEntries[i]
    const percentage = (count / languageTotal.value) * 100
    const angle = (percentage / 100) * 360

    if (angle < 1) continue // 跳过太小的片段

    const startAngle = (currentAngle * Math.PI) / 180
    const endAngle = ((currentAngle + angle) * Math.PI) / 180

    // 计算外圆弧的起点和终点
    const x1 = centerX + radius * Math.cos(startAngle)
    const y1 = centerY + radius * Math.sin(startAngle)
    const x2 = centerX + radius * Math.cos(endAngle)
    const y2 = centerY + radius * Math.sin(endAngle)

    // 计算内圆弧的起点和终点
    const x3 = centerX + innerRadius * Math.cos(endAngle)
    const y3 = centerY + innerRadius * Math.sin(endAngle)
    const x4 = centerX + innerRadius * Math.cos(startAngle)
    const y4 = centerY + innerRadius * Math.sin(startAngle)

    // 判断是否为大弧
    const largeArcFlag = angle > 180 ? 1 : 0

    // 创建SVG路径
    const path = [
      `M ${x1} ${y1}`, // 移动到外圆弧起点
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`, // 外圆弧
      `L ${x3} ${y3}`, // 连接到内圆弧终点
      `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}`, // 内圆弧
      'Z' // 闭合路径
    ].join(' ')

    segments.push({
      language: lang,
      percentage,
      path
    })

    currentAngle += angle
  }

  return segments
})

// 使用 defineOgImageComponent 来定义OG图片
defineOgImageComponent('OgImageGitHubAnalysis', {
  // 从查询参数传递数据
  userName: route.query.userName as string || 'GitHub User',
  userAvatar: route.query.userAvatar as string || '/image/avator.png',
  userLogin: route.query.userLogin as string || '',
  userBio: route.query.userBio as string || '',
  userRole: route.query.userRole as string || 'Developer',
  
  repositories: parseInt(route.query.repositories as string) || 0,
  stars: parseInt(route.query.stars as string) || 0,
  pullRequests: parseInt(route.query.pullRequests as string) || 0,
  additions: parseInt(route.query.additions as string) || 0,
  deletions: parseInt(route.query.deletions as string) || 0,
  workExperience: parseInt(route.query.workExperience as string) || 0,
  
  income: parseInt(route.query.income as string) || 0,
  
  roleModelName: route.query.roleModelName as string || '',
  roleModelAvatar: route.query.roleModelAvatar as string || '',
  roleModelTitle: route.query.roleModelTitle as string || '',
  roleModelAchievement: route.query.roleModelAchievement as string || '',
  
  languages: route.query.languages as string || '{}',
  languageTotal: parseInt(route.query.languageTotal as string) || 0,
})

// 禁用页面的默认布局和导航
definePageMeta({
  layout: false,
})

// 设置页面头部
useHead({
  title: 'GitHub Analysis OG Image',
  meta: [
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})
</script>

<template>
  <div 
    class="w-[1200px] h-[630px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] p-6 relative overflow-hidden"
    data-card-id="share-card-github"
  >
    <div class="bg-[url(/image/graphbg.png)] bg-right bg-contain bg-no-repeat">
      <div class="w-[850px]">
        <!-- 顶部用户信息 -->
        <div class="flex items-center justify-start h-[80px]" v-if="user">
          <img :src="user.avatar" class="w-20 h-20 rounded-full mr-4" />
          <div class="flex flex-col flex-1 justify-around">
            <div class="flex items-center gap-4">
              <h2 class="text-xl font-bold whitespace-nowrap truncate max-w-[300px]">{{ user.name }}</h2>
              <div class="flex items-start gap-1 text-sm text-gray-500 flex-1">
                <span class="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs mt-0.5 flex-shrink-0">✓</span>
                <span class="line-clamp-2">{{ user.bio || user.role }}</span>
              </div>
            </div>
            <div class="flex items-center" v-if="user?.login">
              <span class="text-blue-600 text-sm">
                www.github.com/{{ user.login }}
              </span>
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between mt-6 gap-4">
          <!-- 左侧：Overview 和语言分布 -->
          <div
            class="flex flex-col justify-between p-4 flex-1 h-[400px] w-[450px] border border-gray-200 bg-[#FFFFFF]/60 shadow-lg"
            style="backdrop-filter: blur(14px); border-radius: 15px;"
          >
            <div>
              <div class="flex items-center font-bold gap-2 mb-4">
                <span class="w-4 h-4 bg-blue-500 rounded flex items-center justify-center text-white text-xs">📊</span>
                <span>Overview</span>
              </div>
              <div class="mb-4">
                <!-- SegmentTable 模拟 -->
                <div class="grid grid-cols-3 gap-0 w-full">
                  <div 
                    v-for="(item, index) in insightsItems" 
                    :key="index"
                    class="flex flex-col items-center justify-center text-center min-h-[60px] p-2 relative"
                    :class="{ 
                      'border-r border-gray-200': (index + 1) % 3 !== 0 && index < insightsItems.length - 1
                    }"
                  >
                    <span class="text-xl font-bold text-black mb-1" style="font-family: 'UDC 1.04', sans-serif;">
                      {{ typeof item.value === 'number' ? item.value.toLocaleString() : item.value }}
                    </span>
                    <span class="text-xs text-gray-600">{{ item.label }}</span>
                  </div>
                </div>
              </div>
              <!-- SVG饼图 -->
              <div class="mb-4">
                <div class="h-[180px] w-full flex items-center justify-center">
                  <!-- SVG饼图 -->
                  <div class="relative w-40 h-40">
                    <svg width="160" height="160" viewBox="0 0 160 160" class="transform -rotate-90">
                      <!-- 饼图片段 -->
                      <g v-for="(segment, index) in pieSegments" :key="index">
                        <path
                          :d="segment.path"
                          :fill="pieColors[index % pieColors.length]"
                          stroke="white"
                          stroke-width="2"
                        />
                      </g>
                    </svg>

                    <!-- 中心文字 -->
                    <div class="absolute inset-0 flex items-center justify-center">
                      <div class="text-center">
                        <div class="text-xl font-bold text-gray-800">
                          {{ formatNumber(languageTotal) }}
                        </div>
                        <div class="text-xs text-gray-600">Total Code</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex items-center justify-between gap-2">
              <div class="w-[180px] h-[80px] bg-gradient-to-br from-[#D0D5E3] to-[#B8C5E0] p-3 flex flex-col justify-between rounded">
                <div class="flex items-center gap-1">
                  <span class="text-xs">+</span>
                  <span class="text-[#5F6D94] font-bold text-xs">Additions</span>
                </div>
                <div class="text-[#5F6D94] font-semibold text-xl text-center">
                  {{ formatNumber(additions) }}
                </div>
              </div>
              <div class="w-[180px] h-[80px] bg-gradient-to-br from-[#F8E9C8] to-[#E6D4B7] p-3 flex flex-col justify-between rounded">
                <div class="flex items-center gap-1">
                  <span class="text-xs">-</span>
                  <span class="text-[#CB7C5D] font-bold text-xs">Deletions</span>
                </div>
                <div class="text-[#CB7C5D] font-semibold text-xl text-center">
                  {{ formatNumber(deletions) }}
                </div>
              </div>
            </div>
            </div>
          </div>

          <!-- 右侧：年收入和榜样信息 -->
          <div
            class="flex flex-col justify-between h-[400px] w-[450px] p-4 flex-1 border bg-[#FFFFFF]/60 border-gray-200 shadow-lg"
            style="backdrop-filter: blur(14px); border-radius: 15px;"
          >
            <div>
              <div class="flex items-center font-bold gap-2 mb-4">
                <span class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center text-white text-xs">🎯</span>
                <span>Highlight</span>
              </div>
              <div class="flex flex-col gap-3">
                <!-- 特色项目 -->
                <div
                  v-if="featureProject"
                  class="border-l-4 border-[#CB7C5D] bg-[#FAF2EF] px-3 py-3 rounded"
                >
                  <div class="text-sm font-bold leading-tight border-b border-[#F2E8E4] text-[#2C2C2C] pb-1 mb-2">
                    {{ featureProject.name }}
                  </div>
                  <div class="text-xs font-normal leading-4 text-[#4D4846] mb-2 line-clamp-2">
                    {{ featureProject.description }}
                  </div>
                  <div class="flex items-center gap-4">
                    <div class="flex items-center gap-1">
                      <span class="text-xs">⭐</span>
                      <span class="text-[#969696] text-xs">{{ featureProject.stargazerCount.toLocaleString() }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <span class="text-xs">🍴</span>
                      <span class="text-[#969696] text-xs">{{ featureProject.forkCount.toLocaleString() }}</span>
                    </div>
                  </div>
                </div>
                
                <!-- Most Valuable Pull Request -->
                <div
                  v-if="mostValuablePR"
                  class="border-l-4 border-[#CB7C5D] bg-[#FAF2EF] px-3 py-3 rounded"
                >
                  <div class="text-xs font-bold border-b border-[#F2E8E4] text-[#2C2C2C] leading-4 pb-1 mb-1">
                    {{ mostValuablePR.title }}
                  </div>
                  <div class="text-xs font-normal leading-4 text-[#4D4846] py-1 line-clamp-2">
                    {{ mostValuablePR.impact }}
                  </div>
                  <div class="text-xs text-[#969696]">
                    {{ mostValuablePR.repository }}
                  </div>
                </div>
              </div>
            </div>
            
            <div class="flex items-center justify-between gap-2 mt-4">
              <div class="w-[180px] h-[80px] bg-gradient-to-br from-[#D0D5E3] to-[#B8C5E0] p-3 flex flex-col justify-between rounded">
                <div class="flex items-center gap-1">
                  <span class="text-xs">📈</span>
                  <span class="text-[#5F6D94] font-bold text-xs">Market Value</span>
                </div>
                <div class="text-[#5F6D94] font-semibold text-xl text-center">
                  ${{ valuationLevel ? formatMinSalaryWithSuffix(valuationLevel.salary_range) : formatNumber(income) }}
                </div>
              </div>
              <div class="w-[180px] h-[80px] bg-gradient-to-br from-[#F8E9C8] to-[#E6D4B7] p-3 flex flex-col justify-between rounded">
                <div class="flex items-center gap-1">
                  <span class="text-xs">📊</span>
                  <span class="text-[#CB7C5D] font-bold text-xs">YoE</span>
                </div>
                <div class="text-[#CB7C5D] font-semibold text-xl text-center">
                  {{ workExperience }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右下角小头像 -->
        <img
          :src="user?.avatar || '/image/avator.png'"
          alt=""
          class="absolute top-[280px] right-[150px] w-[50px] h-[50px] rounded-full"
        />
      </div>
      
      <!-- 底部logo和按钮区域 -->
      <div class="flex items-center justify-between border-t border-gray-200 h-[60px] mt-4">
        <img
          src="/image/newlogo1.png"
          width="80"
          height="35"
          alt="DINQ logo"
        />
        <div class="flex items-center gap-2">
          <div class="text-xs text-gray-500">
            Copyright @ 2025 DINQ Inc. All rights reserved
          </div>
          <img src="/image/qrcode.png" alt="QR Code" class="w-10 h-10" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
