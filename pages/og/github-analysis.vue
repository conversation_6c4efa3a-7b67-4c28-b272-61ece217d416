<script setup lang="ts">
// 从查询参数获取数据
const route = useRoute()

// 构建用户数据
const user = computed(() => ({
  name: route.query.userName as string || 'GitHub User',
  avatar: route.query.userAvatar as string || '/image/avator.png',
  role: route.query.userRole as string || 'Developer',
  login: route.query.userLogin as string || '',
  bio: route.query.userBio as string || '',
}))

// 构建统计数据
const stats = computed(() => ({
  repositories: parseInt(route.query.repositories as string) || 0,
  stars: parseInt(route.query.stars as string) || 0,
  pullRequests: parseInt(route.query.pullRequests as string) || 0,
}))

// 解析编程语言数据
const languages = computed(() => {
  try {
    return route.query.languages ? JSON.parse(route.query.languages as string) : {}
  } catch {
    return {}
  }
})

const languageTotal = computed(() => parseInt(route.query.languageTotal as string) || 0)
const income = computed(() => parseInt(route.query.income as string) || 0)
const additions = computed(() => parseInt(route.query.additions as string) || 0)
const deletions = computed(() => parseInt(route.query.deletions as string) || 0)
const workExperience = computed(() => parseInt(route.query.workExperience as string) || 0)

// 格式化数字函数
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(0) + 'k'
  }
  return num.toString()
}

// 构建insightsItems数据
const insightsItems = computed(() => [
  { label: 'GitHub Stars', value: stats.value.stars.toLocaleString() },
  { label: 'Work Experience', value: workExperience.value },
  { label: 'Repositories', value: stats.value.repositories.toLocaleString() },
  { label: 'Total PRs', value: stats.value.pullRequests.toLocaleString() },
  { label: 'Additions', value: formatNumber(additions.value) },
  { label: 'Deletions', value: formatNumber(deletions.value) },
])

// 构建特色项目数据
const featureProject = computed(() => {
  const projectName = route.query.featureProjectName as string
  if (!projectName) return null
  return {
    name: projectName,
    description: route.query.featureProjectDescription as string || 'A high-performance project',
    stargazerCount: parseInt(route.query.featureProjectStars as string) || 0,
    forkCount: parseInt(route.query.featureProjectForks as string) || 0,
  }
})

// 构建最有价值PR数据
const mostValuablePR = computed(() => {
  const prTitle = route.query.mostValuablePRTitle as string
  if (!prTitle) return null
  return {
    title: prTitle,
    impact: route.query.mostValuablePRImpact as string || 'Significant improvement',
    repository: route.query.mostValuablePRRepository as string || 'repository',
  }
})

// 构建薪资级别数据
const valuationLevel = computed(() => {
  const salaryRange = route.query.salaryRange as string
  if (!salaryRange) return null
  return {
    salary_range: salaryRange
  }
})

// 格式化薪资显示
const formatMinSalaryWithSuffix = (salaryRange: string): string => {
  if (!salaryRange) return '120'
  
  const match = salaryRange.match(/\$?(\d+)/)
  if (match) {
    const num = parseInt(match[1])
    if (num >= 1000) {
      return `${Math.floor(num / 1000)}`
    }
    return num.toString()
  }
  return '120'
}

// 使用 defineOgImage 来定义OG图片
defineOgImage({
  component: 'OgImageGitHubAnalysis',
  props: {
    userName: route.query.userName as string || 'GitHub User',
    userAvatar: route.query.userAvatar as string || '/image/avator.png',
    userLogin: route.query.userLogin as string || '',
    userBio: route.query.userBio as string || '',
    userRole: route.query.userRole as string || 'Developer',
    repositories: parseInt(route.query.repositories as string) || 0,
    stars: parseInt(route.query.stars as string) || 0,
    pullRequests: parseInt(route.query.pullRequests as string) || 0,
    additions: parseInt(route.query.additions as string) || 0,
    deletions: parseInt(route.query.deletions as string) || 0,
    workExperience: parseInt(route.query.workExperience as string) || 0,
    income: parseInt(route.query.income as string) || 0,
    languages: route.query.languages as string || '{}',
    languageTotal: parseInt(route.query.languageTotal as string) || 0,
  }
})

// 禁用页面的默认布局和导航
definePageMeta({
  layout: false,
})

// 设置页面头部
useHead({
  title: 'GitHub Analysis OG Image',
  meta: [
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})
</script>

<template>
  <div 
    style="
      display: flex;
      flex-direction: column;
      width: 1200px;
      height: 630px;
      background: linear-gradient(to bottom, #FFFFFF, #F4F2F1);
      padding: 24px;
      position: relative;
    "
  >
    <!-- 顶部用户信息 -->
    <div style="display: flex; align-items: center; height: 80px; margin-bottom: 24px;" v-if="user">
      <img :src="user.avatar" style="width: 80px; height: 80px; border-radius: 50%; margin-right: 16px;" />
      <div style="display: flex; flex-direction: column; flex: 1;">
        <div style="display: flex; align-items: center; margin-bottom: 8px;">
          <h2 style="font-size: 24px; font-weight: bold; margin: 0; margin-right: 16px;">{{ user.name }}</h2>
          <span style="font-size: 14px; color: #666;">{{ user.bio || user.role }}</span>
        </div>
        <div v-if="user?.login" style="font-size: 14px; color: #0066cc;">
          www.github.com/{{ user.login }}
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div style="display: flex; gap: 24px; flex: 1;">
      <!-- 左侧：Overview -->
      <div style="
        display: flex;
        flex-direction: column;
        flex: 1;
        padding: 24px;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid #e5e5e5;
        border-radius: 15px;
      ">
        <div style="display: flex; align-items: center; margin-bottom: 24px;">
          <span style="
            width: 16px;
            height: 16px;
            background: #3b82f6;
            border-radius: 4px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
          ">📊</span>
          <span style="font-weight: bold;">Overview</span>
        </div>
        
        <!-- 统计数据网格 -->
        <div style="
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px;
          margin-bottom: 24px;
        ">
          <div 
            v-for="(item, index) in insightsItems" 
            :key="index"
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              text-align: center;
              padding: 12px;
            "
          >
            <span style="font-size: 20px; font-weight: bold; margin-bottom: 4px;">
              {{ typeof item.value === 'number' ? item.value.toLocaleString() : item.value }}
            </span>
            <span style="font-size: 12px; color: #666;">{{ item.label }}</span>
          </div>
        </div>

        <!-- 编程语言 -->
        <div style="display: flex; flex-direction: column; align-items: center;">
          <div style="font-size: 32px; font-weight: bold; margin-bottom: 8px;">
            {{ formatNumber(languageTotal) }}
          </div>
          <div style="font-size: 14px; color: #666; margin-bottom: 16px;">Total Code</div>
          <div style="display: flex; flex-direction: column; gap: 4px;">
            <div 
              v-for="([lang, count], index) in Object.entries(languages).slice(0, 3)" 
              :key="lang"
              style="display: flex; justify-content: space-between; font-size: 12px; min-width: 120px;"
            >
              <span>{{ lang }}</span>
              <span style="font-weight: 500;">{{ ((count / languageTotal) * 100).toFixed(1) }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：Highlight -->
      <div style="
        display: flex;
        flex-direction: column;
        flex: 1;
        padding: 24px;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid #e5e5e5;
        border-radius: 15px;
      ">
        <div style="display: flex; align-items: center; margin-bottom: 24px;">
          <span style="
            width: 16px;
            height: 16px;
            background: #f97316;
            border-radius: 4px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
          ">🎯</span>
          <span style="font-weight: bold;">Highlight</span>
        </div>
        
        <div style="display: flex; flex-direction: column; gap: 16px; flex: 1;">
          <!-- 特色项目 -->
          <div
            v-if="featureProject"
            style="
              border-left: 4px solid #CB7C5D;
              background: #FAF2EF;
              padding: 16px;
              border-radius: 8px;
            "
          >
            <div style="font-size: 14px; font-weight: bold; margin-bottom: 8px; color: #2C2C2C;">
              {{ featureProject.name }}
            </div>
            <div style="font-size: 12px; color: #4D4846; margin-bottom: 8px; line-height: 1.4;">
              {{ featureProject.description }}
            </div>
            <div style="display: flex; gap: 16px; font-size: 12px; color: #969696;">
              <span>⭐ {{ featureProject.stargazerCount.toLocaleString() }}</span>
              <span>🍴 {{ featureProject.forkCount.toLocaleString() }}</span>
            </div>
          </div>
          
          <!-- Most Valuable Pull Request -->
          <div
            v-if="mostValuablePR"
            style="
              border-left: 4px solid #CB7C5D;
              background: #FAF2EF;
              padding: 16px;
              border-radius: 8px;
            "
          >
            <div style="font-size: 12px; font-weight: bold; margin-bottom: 4px; color: #2C2C2C;">
              {{ mostValuablePR.title }}
            </div>
            <div style="font-size: 12px; color: #4D4846; margin-bottom: 4px; line-height: 1.4;">
              {{ mostValuablePR.impact }}
            </div>
            <div style="font-size: 12px; color: #969696;">
              {{ mostValuablePR.repository }}
            </div>
          </div>
        </div>
        
        <!-- 底部卡片 -->
        <div style="display: flex; gap: 16px; margin-top: 16px;">
          <div style="
            flex: 1;
            height: 80px;
            background: linear-gradient(135deg, #D0D5E3, #B8C5E0);
            padding: 12px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
          ">
            <div style="display: flex; align-items: center; gap: 4px;">
              <span style="font-size: 12px;">📈</span>
              <span style="color: #5F6D94; font-weight: bold; font-size: 12px;">Market Value</span>
            </div>
            <div style="color: #5F6D94; font-weight: 600; font-size: 20px; text-align: center;">
              ${{ valuationLevel ? formatMinSalaryWithSuffix(valuationLevel.salary_range) : formatNumber(income) }}
            </div>
          </div>
          <div style="
            flex: 1;
            height: 80px;
            background: linear-gradient(135deg, #F8E9C8, #E6D4B7);
            padding: 12px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
          ">
            <div style="display: flex; align-items: center; gap: 4px;">
              <span style="font-size: 12px;">📊</span>
              <span style="color: #CB7C5D; font-weight: bold; font-size: 12px;">YoE</span>
            </div>
            <div style="color: #CB7C5D; font-weight: 600; font-size: 20px; text-align: center;">
              {{ workExperience }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部logo -->
    <div style="
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-top: 1px solid #e5e5e5;
      height: 60px;
      margin-top: 16px;
      padding-top: 16px;
    ">
      <div style="font-size: 18px; font-weight: bold; color: #333;">DINQ</div>
      <div style="font-size: 12px; color: #666;">
        Copyright @ 2025 DINQ Inc. All rights reserved
      </div>
    </div>
  </div>
</template>
