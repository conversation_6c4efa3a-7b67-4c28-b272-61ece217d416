<script setup lang="ts">
// 从查询参数获取数据
const route = useRoute()

// 构建用户数据
const user = computed(() => ({
  name: route.query.userName as string || 'GitHub User',
  avatar: route.query.userAvatar as string || '/image/avator.png',
  role: route.query.userRole as string || 'Developer',
  login: route.query.userLogin as string || '',
  bio: route.query.userBio as string || '',
}))

// 构建统计数据
const stats = computed(() => ({
  repositories: parseInt(route.query.repositories as string) || 0,
  stars: parseInt(route.query.stars as string) || 0,
  pullRequests: parseInt(route.query.pullRequests as string) || 0,
}))

// 构建角色模型数据
const roleModel = computed(() => {
  if (!route.query.roleModelName) return null
  return {
    name: route.query.roleModelName as string,
    avatar: route.query.roleModelAvatar as string || '/image/avator.png',
    title: route.query.roleModelTitle as string || '',
    achievement: route.query.roleModelAchievement as string || '',
  }
})

// 解析编程语言数据
const languages = computed(() => {
  try {
    return route.query.languages ? JSON.parse(route.query.languages as string) : {}
  } catch {
    return {}
  }
})

// 构建insightsItems数据（模拟SegmentTable的数据）
const insightsItems = computed(() => [
  { label: 'GitHub Stars', value: stats.value.stars.toLocaleString() },
  { label: 'Work Experience', value: workExperience.value },
  { label: 'Total Issues', value: '0' }, // 暂时使用默认值
  { label: 'Repositories', value: stats.value.repositories.toLocaleString() },
  { label: 'Total PRs', value: stats.value.pullRequests.toLocaleString() },
])

// 构建特色项目数据
const featureProject = computed(() => {
  const projectName = route.query.featureProjectName as string
  if (!projectName) return null
  return {
    name: projectName,
    description: route.query.featureProjectDescription as string || 'A high-performance project',
    stargazerCount: parseInt(route.query.featureProjectStars as string) || 0,
    forkCount: parseInt(route.query.featureProjectForks as string) || 0,
    url: route.query.featureProjectUrl as string || '#',
  }
})

// 构建最有价值PR数据
const mostValuablePR = computed(() => {
  const prTitle = route.query.mostValuablePRTitle as string
  if (!prTitle) return null
  return {
    title: prTitle,
    impact: route.query.mostValuablePRImpact as string || 'Significant improvement',
    repository: route.query.mostValuablePRRepository as string || 'repository',
    url: route.query.mostValuablePRUrl as string || '#',
  }
})

// 构建薪资级别数据
const valuationLevel = computed(() => {
  const salaryRange = route.query.salaryRange as string
  if (!salaryRange) return null
  return {
    salary_range: salaryRange
  }
})

const languageTotal = computed(() => parseInt(route.query.languageTotal as string) || 0)
const income = computed(() => parseInt(route.query.income as string) || 0)
const additions = computed(() => parseInt(route.query.additions as string) || 0)
const deletions = computed(() => parseInt(route.query.deletions as string) || 0)
const workExperience = computed(() => parseInt(route.query.workExperience as string) || 0)

// 格式化数字函数
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(0) + 'k'
  }
  return num.toString()
}

// 格式化薪资显示
const formatMinSalaryWithSuffix = (salaryRange: string): string => {
  if (!salaryRange) return '120K'

  // 提取第一个数字
  const match = salaryRange.match(/\$?(\d+)/)
  if (match) {
    const num = parseInt(match[1])
    if (num >= 1000) {
      return `${Math.floor(num / 1000)}K`
    }
    return num.toString()
  }
  return '120K'
}

// 使用 defineOgImageComponent 来定义OG图片
defineOgImageComponent('OgImageGitHubAnalysis', {
  // 从查询参数传递数据
  userName: route.query.userName as string || 'GitHub User',
  userAvatar: route.query.userAvatar as string || '/image/avator.png',
  userLogin: route.query.userLogin as string || '',
  userBio: route.query.userBio as string || '',
  userRole: route.query.userRole as string || 'Developer',

  repositories: parseInt(route.query.repositories as string) || 0,
  stars: parseInt(route.query.stars as string) || 0,
  pullRequests: parseInt(route.query.pullRequests as string) || 0,
  additions: parseInt(route.query.additions as string) || 0,
  deletions: parseInt(route.query.deletions as string) || 0,
  workExperience: parseInt(route.query.workExperience as string) || 0,

  income: parseInt(route.query.income as string) || 0,

  roleModelName: route.query.roleModelName as string || '',
  roleModelAvatar: route.query.roleModelAvatar as string || '',
  roleModelTitle: route.query.roleModelTitle as string || '',
  roleModelAchievement: route.query.roleModelAchievement as string || '',

  languages: route.query.languages as string || '{}',
  languageTotal: parseInt(route.query.languageTotal as string) || 0,
})

// 禁用页面的默认布局和导航
definePageMeta({
  layout: false,
})

// 设置页面头部
useHead({
  title: 'GitHub Analysis OG Image',
  meta: [
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})
</script>

<template>
  <div
    class="w-[1200px] h-[630px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] p-6 relative overflow-hidden"
    data-card-id="share-card-github"
  >
    <!-- 背景图案 -->
    <div class="absolute inset-0 bg-[url(/image/graphbg.png)] bg-right bg-contain bg-no-repeat opacity-30"></div>

    <div class="relative w-full" style="z-index: 10;">
      <!-- 顶部用户信息 -->
      <div class="flex items-center justify-start h-[80px]" v-if="user">
        <img :src="user.avatar" class="w-20 h-20 rounded-full mr-4 border-2 border-white shadow-lg" />
        <div class="flex flex-col flex-1 justify-around">
          <div class="flex items-center gap-4">
            <h2 class="text-2xl font-bold text-gray-900">{{ user.name }}</h2>
            <div class="flex items-start gap-1 text-sm text-gray-600 flex-1">
              <span class="line-clamp-2">{{ user.bio || user.role }}</span>
            </div>
          </div>
          <div class="flex items-center" v-if="user?.login">
            <span class="text-blue-600 text-sm font-medium">
              github.com/{{ user.login }}
            </span>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-between mt-6 gap-4">
        <!-- 左侧：Overview 和语言分布 -->
        <div
          class="flex flex-col justify-between p-4 flex-1 h-[475px] w-[450px] border border-gray-200 bg-[#FFFFFF]/60 shadow-lg"
          style="backdrop-filter: blur(14px); border-radius: 15px;"
        >
          <div>
            <div class="flex items-center font-bold gap-2 mb-4">
              <span class="text-lg">📊</span>
              <span>Overview</span>
            </div>
            <div class="mb-4">
              <!-- SegmentTable 模拟 -->
              <div class="grid grid-cols-3 gap-0 w-full">
                <div
                  v-for="(item, index) in insightsItems"
                  :key="index"
                  class="flex flex-col items-center justify-center text-center min-h-[60px] p-2 relative"
                  :class="{
                    'border-r border-gray-200': (index + 1) % 3 !== 0 && index < insightsItems.length - 1
                  }"
                >
                  <span class="text-xl font-bold text-black mb-1" style="font-family: 'UDC 1.04', sans-serif;">
                    {{ typeof item.value === 'number' ? item.value.toLocaleString() : item.value }}
                  </span>
                  <span class="text-xs text-gray-600">{{ item.label }}</span>
                </div>
              </div>
            </div>
            <!-- 简化的语言分布 -->
            <div class="mb-4">
              <div class="h-[180px] w-full flex items-center justify-center">
                <div class="text-center">
                  <div class="text-4xl font-bold text-gray-800 mb-2">
                    {{ formatNumber(languageTotal) }}
                  </div>
                  <div class="text-sm text-gray-600 mb-4">Total Code</div>
                  <div class="space-y-1">
                    <div
                      v-for="([lang, count], index) in Object.entries(languages).slice(0, 3)"
                      :key="lang"
                      class="flex items-center justify-between text-sm"
                    >
                      <span class="text-gray-700">{{ lang }}</span>
                      <span class="font-medium">{{ ((count / languageTotal) * 100).toFixed(1) }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center justify-between gap-2">
            <div
              class="w-[120px] h-[80px] bg-gradient-to-br from-[#D0D5E3] to-[#B8C5E0] p-3 flex flex-col justify-between rounded"
            >
              <div class="flex items-center gap-1">
                <span class="flex items-center justify-center w-4 h-4 rounded bg-white text-xs">+</span>
                <span class="text-[#5F6D94] font-bold text-xs">Additions</span>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold text-[#5F6D94]">{{ formatNumber(additions) }}</div>
              </div>
            </div>
            <div
              class="w-[120px] h-[80px] bg-gradient-to-br from-[#F8E9C8] to-[#E6D4B7] p-3 flex flex-col justify-between rounded"
            >
              <div class="flex items-center gap-1">
                <span class="flex items-center justify-center w-4 h-4 rounded bg-white text-xs">-</span>
                <span class="text-[#CB7C5D] font-bold text-xs">Deletions</span>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold text-[#CB7C5D]">{{ formatNumber(deletions) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：特色项目、最有价值PR、市场价值 -->
        <div class="flex flex-col gap-4 w-[350px]">
          <!-- 特色项目 -->
          <div
            v-if="featureProject"
            class="p-4 h-[150px] border border-gray-200 bg-[#FFFFFF]/60 shadow-lg"
            style="backdrop-filter: blur(14px); border-radius: 15px;"
          >
            <div class="flex items-center font-bold gap-2 mb-3">
              <span>🚀</span>
              <span>Featured Project</span>
            </div>
            <div class="space-y-2">
              <div class="font-semibold text-gray-900">{{ featureProject.name }}</div>
              <div class="text-sm text-gray-600 line-clamp-2">{{ featureProject.description }}</div>
              <div class="flex items-center gap-4 text-sm">
                <span class="flex items-center gap-1">
                  <span>⭐</span>
                  <span>{{ featureProject.stargazerCount.toLocaleString() }}</span>
                </span>
                <span class="flex items-center gap-1">
                  <span>🍴</span>
                  <span>{{ featureProject.forkCount.toLocaleString() }}</span>
                </span>
              </div>
            </div>
          </div>

          <!-- 最有价值PR -->
          <div
            v-if="mostValuablePR"
            class="p-4 h-[150px] border border-gray-200 bg-[#FFFFFF]/60 shadow-lg"
            style="backdrop-filter: blur(14px); border-radius: 15px;"
          >
            <div class="flex items-center font-bold gap-2 mb-3">
              <span>💎</span>
              <span>Most Valuable PR</span>
            </div>
            <div class="space-y-2">
              <div class="font-semibold text-gray-900 text-sm line-clamp-2">{{ mostValuablePR.title }}</div>
              <div class="text-sm text-gray-600 line-clamp-2">{{ mostValuablePR.impact }}</div>
              <div class="text-xs text-blue-600">{{ mostValuablePR.repository }}</div>
            </div>
          </div>

          <!-- 市场价值 -->
          <div
            class="p-4 h-[150px] border border-gray-200 bg-[#FFFFFF]/60 shadow-lg"
            style="backdrop-filter: blur(14px); border-radius: 15px;"
          >
            <div class="flex items-center font-bold gap-2 mb-3">
              <span>💰</span>
              <span>Market Value</span>
            </div>
            <div class="space-y-2">
              <div v-if="roleModel" class="flex items-center gap-3">
                <img :src="roleModel.avatar" class="w-10 h-10 rounded-full" />
                <div>
                  <div class="font-semibold text-sm">{{ roleModel.name }}</div>
                  <div class="text-xs text-gray-600">{{ roleModel.title }}</div>
                </div>
              </div>
              <div class="text-center mt-3">
                <div class="text-2xl font-bold text-green-600">
                  ${{ valuationLevel ? formatMinSalaryWithSuffix(valuationLevel.salary_range) : formatNumber(income) }}
                </div>
                <div class="text-xs text-gray-600">Annual Salary</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部品牌信息 -->
      <div class="absolute bottom-2 right-4 flex items-center gap-2">
        <div class="text-xs text-gray-500">
          Copyright @ 2025 DINQ Inc. All rights reserved
        </div>
        <img src="/image/qrcode.png" alt="QR Code" class="w-12 h-12" />
      </div>
    </div>
  </div>
</template>



<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 确保页面适合OG图片尺寸 */
html, body {
  margin: 0;
  padding: 0;
  width: 1200px;
  height: 630px;
  overflow: hidden;
}
</style>
