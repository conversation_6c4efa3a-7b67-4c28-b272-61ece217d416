<template>
  <div class="px-30 h-full" style="margin-top: 4rem">
    <!-- 激活码弹窗 - 使用 ClientOnly 避免 hydration 问题 -->
    <ClientOnly>
      <InviteCodeModal
        v-if="showInviteModal"
        :error="inviteError"
        :loading="inviteLoading"
        @close="showInviteModal = false"
        @submit="handleSubmitActivationCode"
        @waiting-list="onShowWaitingListModal"
      />
      <WaitingListModal
        v-if="showWaitingListModal"
        @close="showWaitingListModal = false"
        @back="onBackToInviteCode"
      />
      <div
        v-if="inviteSuccess"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
      >
      <div
        class="bg-white rounded-2xl shadow-xl p-8 pt-7 pb-7 w-full max-w-96 relative text-center"
      >
        <div class="text-lg font-semibold text-black mb-6">
          Enter Invite Code
          <button
            class="absolute top-6 right-6 text-gray-400 hover:text-gray-600 transition-colors text-xl"
            @click="inviteSuccess = false"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 5L5 15M5 5L15 15"
                stroke="#BDBDBD"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <div class="flex justify-center mb-4">
          <div class="bg-green-500 rounded-full w-12 h-12 flex items-center justify-center mx-auto">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="16" fill="none" />
              <path
                d="M10 17.5L14 21.5L22 13.5"
                stroke="#fff"
                stroke-width="2.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        <div class="text-xl font-bold text-black mb-2">Verification Successful</div>
        <div class="text-base text-gray-700 mb-7">
          Congratulations! Your invitation code has been verified successfully
        </div>
        <button
          class="w-full h-12 bg-black text-white rounded-lg font-semibold text-base transition hover:bg-gray-900"
          @click="goHome"
        >
          Ok
        </button>
      </div>
    </div>
    </ClientOnly>

    <template v-if="!isMounted || loading || isLoadingJson">
      <!-- 骨架屏组件 -->
      <div class="min-h-[60vh]">
        <!-- PK卡片骨架屏 -->
        <div class="relative">
          <div class="grid grid-cols-2 gap-7.5 mb-7.5">
            <!-- 左侧研究者骨架屏 -->
            <div class="bg-[#9BA3C1]/20 dark:bg-gray-600/20 rounded-2xl p-7.5 relative animate-pulse">
              <div class="absolute -top-5 left-7.5">
                <div class="w-15 h-15 rounded-full bg-gray-200/30 dark:bg-gray-600/30 border-4 border-white dark:border-gray-800"></div>
              </div>
              <div class="mt-7.5">
                <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded w-3/4 mb-3"></div>
                <div class="h-5 bg-gray-200/25 dark:bg-gray-600/25 rounded w-1/2 mb-4"></div>
                <div class="flex flex-wrap gap-2 mb-4">
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-16"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-6 bg-gray-200/25 dark:bg-gray-600/25 rounded w-5/6"></div>
                  <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
                </div>
              </div>
            </div>

            <!-- 右侧研究者骨架屏 -->
            <div class="bg-[#C6A69B]/20 dark:bg-gray-600/20 rounded-2xl p-7.5 relative animate-pulse">
              <div class="absolute -top-5 left-7.5">
                <div class="w-15 h-15 rounded-full bg-gray-200/30 dark:bg-gray-600/30 border-4 border-white dark:border-gray-800"></div>
              </div>
              <div class="mt-7.5">
                <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded w-3/4 mb-3"></div>
                <div class="h-5 bg-gray-200/25 dark:bg-gray-600/25 rounded w-1/2 mb-4"></div>
                <div class="flex flex-wrap gap-2 mb-4">
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-16"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-6 bg-gray-200/25 dark:bg-gray-600/25 rounded w-5/6"></div>
                  <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- VS标志骨架屏 -->
          <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10">
            <div class="w-20 h-20 rounded-full bg-gray-200/40 dark:bg-gray-600/40 animate-pulse"></div>
          </div>
        </div>

        <Loading :visible="loading" :data="thinking" @update:visible="router.replace('/analysis')" />

        <!-- 雷达图骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse">
            <div class="h-8 bg-gray-100/60 dark:bg-gray-600/60 rounded w-1/3 mx-auto mb-8"></div>
            <div class="h-[300px] bg-gray-100/40 dark:bg-gray-600/40 rounded-lg"></div>
          </div>
        </div>

        <!-- 指标对比骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse space-y-6">
            <div class="h-12 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
            <div class="h-12 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
            <div class="h-12 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
            <div class="h-12 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
          </div>
        </div>

        <!-- 代表项目骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse">
            <div class="h-8 bg-gray-100/60 dark:bg-gray-600/60 rounded w-1/4 mx-auto mb-8"></div>
            <div class="grid grid-cols-2 gap-6">
              <div class="space-y-4">
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-3/4"></div>
                <div class="h-32 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
              </div>
              <div class="space-y-4">
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-3/4"></div>
                <div class="h-32 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="pkData">

      <div class="relative bg-cover bg-center bg-no-repeat rounded-xl compare-page-bg">
        <!-- PK卡片部分 -->
        <div class="grid grid-cols-2 gap-7.5 mb-7.5">
          <!-- 左侧GitHub用户 -->
          <div class="rounded-xl p-5 relative flex flex-col items-center text-center min-h-[320px]">
            <div class="absolute -top-5 left-1/2 -translate-x-1/2">
              <div class="flex items-center gap-2">
                <img
                  :src="pkData.user1.user.avatarUrl || pkData.user1.user.avatar_url || '/image/default-avatar.png'"
                  class="w-15 h-15 rounded-full border-4 border-white object-cover"
                />
              </div>
            </div>
            <div class="mt-7.5 w-full flex flex-col justify-between flex-1">
              <div class="flex flex-col">
                <div class="text-white text-xl font-bold">{{ pkData.user1.name }}</div>
                <div class="text-white dark:text-[#C6C6C6] mt-1.5 flex items-start justify-center text-sm max-w-[400px] mx-auto">
                  <SvgIcon name="verified" class="text-4 mr-1.5 mt-0.5 flex-shrink-0" />
                  <span class="text-center line-clamp-2">{{ pkData.user1.user.bio || 'GitHub Developer' }}</span>
                </div>
              </div>
              <div class="mt-auto">
                <div class="flex flex-wrap gap-1.5 mb-3 justify-center items-center min-h-[32px]">
                  <div
                    v-for="(tag, index) in pkData.user1.user.tags?.slice(0, 3) || []"
                    :key="index"
                    class="px-2.5 py-1 bg-[#A1AED2] dark:bg-[#3C4356] rounded-md text-[#262D3F] dark:text-[#FAF9F5] text-xs whitespace-nowrap"
                  >
                    {{ tag }}
                  </div>
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-sm">
                  Wanna compare
                  <span class="text-[#364A83] dark:text-[#FDB852] font-bold">{{
                    pkData.user1.name
                  }}</span>
                  with other GitHub developers?
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-xs mt-1">
                  Just input their GitHub username
                </div>
                <ClientOnly>
                  <motion.div
                    :initial="{ opacity: 0, y: 10 }"
                    :animate="{ opacity: 1, y: 0 }"
                    :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
                    class="f-cer mt-5 mb-4"
                  >
                    <div
                      class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                    >
                      <SearchInput ref="searchInputRef1" placeholder="GitHub username" @enter-search="handleLeftCompare" />
                      <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleLeftCompare" />
                    </div>
                  </motion.div>
                  <template #fallback>
                    <div class="f-cer mt-5 mb-4">
                      <div
                        class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                      >
                        <SearchInput ref="searchInputRef1" placeholder="GitHub username" @enter-search="handleLeftCompare" />
                        <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleLeftCompare" />
                      </div>
                    </div>
                  </template>
                </ClientOnly>
              </div>
            </div>
          </div>
          <!-- 右侧GitHub用户 -->
          <div class="rounded-xl p-5 relative flex flex-col items-center text-center min-h-[320px]">
            <div class="absolute -top-5 left-1/2 -translate-x-1/2">
              <div class="flex items-center gap-2">
                <img
                  :src="pkData.user2.user.avatarUrl || pkData.user2.user.avatar_url || '/image/default-avatar.png'"
                  class="w-15 h-15 rounded-full border-4 border-white object-cover"
                />
              </div>
            </div>
            <div class="mt-7.5 w-full flex flex-col justify-between flex-1">
              <div class="flex flex-col">
                <div class="text-white text-xl font-bold">{{ pkData.user2.name }}</div>
                <div class="text-white dark:text-[#C6C6C6] mt-1.5 flex items-start justify-center text-sm max-w-[400px] mx-auto">
                  <SvgIcon name="verified-brown" class="text-4 mr-1.5 mt-0.5 flex-shrink-0 text-red-500" />
                  <span class="text-center line-clamp-2">{{ pkData.user2.user.bio || 'GitHub Developer' }}</span>
                </div>
              </div>
              <div class="mt-auto">
                <div class="flex flex-wrap gap-1.5 mb-3 justify-center items-center min-h-[32px]">
                  <div
                    v-for="(tag, index) in pkData.user2.user.tags?.slice(0, 3) || []"
                    :key="index"
                    class="px-2.5 py-1 bg-[#E7CDC3] dark:bg-[#413834] rounded-md text-[#7F4832] dark:text-[#FAF9F5] text-xs whitespace-nowrap"
                  >
                    {{ tag }}
                  </div>
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-sm">
                  Wanna compare
                  <span class="text-[#6D4130] dark:text-[#5765F2] font-bold">{{
                    pkData.user2.name
                  }}</span>
                  with other GitHub developers?
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-xs mt-1">
                  Just input their GitHub username
                </div>
                <ClientOnly>
                  <motion.div
                    :initial="{ opacity: 0, y: 10 }"
                    :animate="{ opacity: 1, y: 0 }"
                    :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
                    class="f-cer mt-5 mb-4"
                  >
                    <div
                      class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                    >
                      <SearchInput ref="searchInputRef2" placeholder="GitHub username" @enter-search="handleRightCompare" />
                      <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleRightCompare" />
                    </div>
                  </motion.div>
                  <template #fallback>
                    <div class="f-cer mt-5 mb-4">
                      <div
                        class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                      >
                        <SearchInput ref="searchInputRef2" placeholder="GitHub username" @enter-search="handleRightCompare" />
                        <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleRightCompare" />
                      </div>
                    </div>
                  </template>
                </ClientOnly>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 雷达图部分 -->
      <div class="rounded-2xl p-7.5 mb-7.5">
        <GitHubRadarChart :user1="pkData.user1" :user2="pkData.user2" size="large" />
      </div>

      <div class="flex items-center justify-center mb-10 flex-col gap-4">
        <div
          class="h-13.5 w-[210px] text-4 font-600 text-white bg-[#CB7C5D] dark:bg-[#654D43] dark:border dark:border-[#866457] fx-cer justify-center rounded-full gap-2 cursor-pointer"
          @click="showCompareCard = true"
          >
          <div class="i-proicons:x-twitter wh-5 font-600" data-v-2dc31878=""></div>
          Share
        </div>
      </div>

      <GitHubShareCardCompare
        :show="showCompareCard"
        :is-dark="isDark"
        :user1="pkData.user1"
        :user2="pkData.user2"
        @close="showCompareCard = false"
      />
      <!-- 指标对比和代表项目部分 -->
      <GitHubCompareMetrics :user1="pkData.user1" :user2="pkData.user2" />

      <!-- 比较部分 -->
      <div class="flex flex-col items-center mt-20">
        <div class="text-[56px] clash-semibold font-semibold text-center max-w-[800px] leading-[72.8px]">
          Or compare the profiles<br />
          of two GitHub developers
        </div>
        <div class="flex items-center gap-4 mt-10 w-full max-w-[800px]">
          <div class="flex-1 sty_f_r_end">
            <input
              v-model="user1Input"
              type="text"
              placeholder="GitHub username"
              class="custom-input w-[280px] h-13.5 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300 min-w-0"
              style="outline: none !important; box-shadow: none !important; border-color: black !important;"
              @keyup.enter="handleCompare"
            />
          </div>
          <div class="text-3xl font-bold italic" style="color: #CB7C5D;">VS</div>
          <div class="flex-1">
            <input
              v-model="user2Input"
              type="text"
              placeholder="GitHub username"
              class="custom-input w-[280px] h-13.5 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300 min-w-0"
              style="outline: none !important; box-shadow: none !important; border-color: black !important;"
              @keyup.enter="handleCompare"
            />
          </div>
        </div>
        <button
          @click="handleCompare"
          class="mt-7.5 flex items-center justify-center gap-2 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors w-[210px] h-[54px] dark:border-3 dark:border-white"
        >
          <img src="/image/stars.png" alt="compare" />
          <span class="text-base font-bold">Compare</span>
        </button>
        <div class="text-sm text-gray-500 mt-7.5 mb-20">
          By clicking Compare you agree to our
          <a href="/terms" class="text-primary-100 hover:text-primary-200 underline"
            >terms of service</a
          >
        </div>
      </div>
    </template>
    <template v-else>
      <!-- 兜底展示 -->
      <div class="min-h-[60vh] flex flex-col items-center justify-center">
        <div class="w-20 h-20 rounded-full bg-[#FDF7F7] flex items-center justify-center mb-6">
          <div class="i-carbon:warning-alt text-primary-100 text-3xl"></div>
        </div>
        <div class="text-[32px] font-bold clash-display text-center mb-4">No Data Available</div>
        <div class="text-gray-600 text-center max-w-[500px] mb-10">
          Sorry, we couldn't find the comparison data. Please try comparing other GitHub developers.
        </div>
        <!-- 比较部分 -->
        <div class="w-full max-w-[800px]">
          <div class="text-[42px] font-bold clash-display text-center leading-[1.2] mb-10">
            Compare the profiles<br />
            of two GitHub developers
          </div>
          <div class="flex items-center gap-4">
            <div class="flex-1">
              <input
                v-model="user1Input"
                type="text"
                placeholder="GitHub username"
                class="w-full h-12 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300"
                style="outline: none !important; box-shadow: none !important; border-color: black !important;"
                @keyup.enter="handleCompare"
              />
            </div>
            <div class="text-3xl font-bold italic" style="color: #CB7C5D;">VS</div>
            <div class="flex-1">
              <input
                v-model="user2Input"
                type="text"
                placeholder="GitHub username"
                class="w-full h-12 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300"
                style="outline: none !important; box-shadow: none !important; border-color: black !important;"
                @keyup.enter="handleCompare"
              />
            </div>
          </div>
          <div class="flex justify-center mt-7.5">
            <button
              @click="handleCompare"
              class="flex items-center justify-center gap-2 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors w-[210px] h-[54px] dark:border-3 dark:border-white"
            >
              <img src="/image/stars.png" alt="compare" />
              <span class="text-base font-bold">Compare</span>
            </button>
          </div>
          <div class="text-sm text-gray-500 text-center mt-7.5 mb-20">
            By clicking Compare you agree to our
            <a href="/terms" class="text-primary-100 hover:text-primary-200 underline"
              >terms of service</a
            >
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
  import stars from '@/assets/image/stars.png'
  import { useRoute, useRouter } from 'vue-router'
  import { useEventStream } from '@/composables/useEventStream'
  import { motion } from "motion-v"
  import GitHubRadarChart from '@/components/GitHubRadarChart.vue'
  import GitHubCompareMetrics from '@/components/GitHubCompareMetrics.vue'
  import GitHubShareCardCompare from '@/components/GitHubShareCardCompare/index.vue'
  import SearchInput from '@/components/SearchInput/index.vue'
  import ActionButton from '@/components/ActionButton/index.vue'
  import SvgIcon from '@/components/SvgIcon/index.vue'
  import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue'
  import InviteCodeModal from '@/components/InviteCodeModal.vue'
  import WaitingListModal from '@/components/WaitingListModal.vue'
  import Loading from '@/components/Loading/index.vue'
  import { submitActivationCode } from '~/api'

  definePageMeta({
    middleware: 'auth',
  })

  // 从 URL 获取用户名用于初始 SEO
  const route = useRoute()
  const user1 = route.query.user1 as string
  const user2 = route.query.user2 as string

  // 设置初始 SEO meta（在服务端渲染时生效）
  if (user1 && user2) {
    const initialTitle = `${user1} vs ${user2} - GitHub Developer Comparison | DINQ`
    const initialDescription = `Compare GitHub developers ${user1} and ${user2}. Analyze their coding skills, project contributions, and development expertise side by side.`

    // 获取当前域名
    const currentDomain = getCurrentDomain()

    useSeoMeta({
      title: initialTitle,
      description: initialDescription,
      keywords: `${user1}, ${user2}, GitHub Comparison, Developer Comparison, GitHub Analysis, Developer Skills, Code Comparison`,

      // Open Graph
      ogTitle: initialTitle,
      ogDescription: initialDescription,
      ogType: 'website',
      ogUrl: `${currentDomain}/github/compare?user1=${user1}&user2=${user2}`,

      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: initialTitle,
      twitterDescription: initialDescription,
    })

    useHead({
      title: initialTitle,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'theme-color',
          content: '#CB7C5D'
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: `${currentDomain}/github/compare?user1=${user1}&user2=${user2}`
        }
      ]
    })
  }

  // 动态 SEO 元数据更新（数据加载后的增强版本）
  const updateCompareSeoMeta = (data: any) => {
    const user1Name = data.user1.name || data.user1.user?.login || 'Developer 1'
    const user2Name = data.user2.name || data.user2.user?.login || 'Developer 2'

    const title = `${user1Name} vs ${user2Name} - GitHub Developer Comparison | DINQ`
    const description = `Compare GitHub developers ${user1Name} and ${user2Name}. Analyze their coding skills, project contributions, and development expertise side by side.`

    // 获取两个开发者的主要编程语言
    const user1Languages = Object.keys(data.user1.code_contribution?.languages || {}).slice(0, 3)
    const user2Languages = Object.keys(data.user2.code_contribution?.languages || {}).slice(0, 3)
    const allLanguages = [...new Set([...user1Languages, ...user2Languages])]

    const keywords = [
      user1Name,
      user2Name,
      'GitHub Comparison',
      'Developer Comparison',
      'GitHub Analysis',
      'Developer Skills',
      'Code Comparison',
      ...allLanguages
    ].join(', ')

    useSeoMeta({
      title,
      description,
      keywords,

      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogType: 'website',
      ogUrl: `${currentDomain}/github/compare?user1=${data.user1.user?.login}&user2=${data.user2.user?.login}`,

      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,

      // 额外的 meta 标签
      author: 'DINQ',
      'article:tag': allLanguages.join(', '),
    })

    // 设置页面标题
    useHead({
      title,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'theme-color',
          content: '#CB7C5D'
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://dinq.io/github/compare?user1=${data.user1.user?.login}&user2=${data.user2.user?.login}`
        }
      ]
    })
  }

  // const route = useRoute() // 已在上面声明
  const router = useRouter()
  const { currentUser } = useFirebaseAuth()

  // Import what we need - connectWithObj and thinking are for SSE fallback only
  const { thinking, loading, connectWithObj, limitInfo, pkData } = useEventStream()

  // Note: thinking is kept for SSE fallback scenarios
  // reportDataInfo removed since we handle direct HTTP responses now

  const user1Input = ref('')
  const user2Input = ref('')

  // 为顶部搜索框添加独立的ref
  const searchInputRef1 = ref()
  const searchInputRef2 = ref()

  // 移除未使用的变量
  // const isAnalyzing = ref(false)
  // const analysisProgress = ref(0)
  let progressInterval: NodeJS.Timeout | null = null

  const isLoadingJson = ref(false)

  // 邀请码相关状态
  const showInviteModal = ref(false)
  const showWaitingListModal = ref(false)
  const inviteError = ref('')
  const inviteLoading = ref(false)
  const inviteSuccess = ref(false)

  // 分享卡片状态
  const showCompareCard = ref(false)

  // 主题状态
  const isDark = ref(false)

  // 检测深色模式 - 仅在客户端执行
  const updateDarkMode = () => {
    if (process.client) {
      isDark.value = document.documentElement.classList.contains('dark')
    }
  }

  // 添加客户端挂载状态
  const isMounted = ref(false)

  // 存储清理函数的引用
  let currentSimulationCleanup: (() => void) | null = null

  onMounted(() => {
    isMounted.value = true
    updateDarkMode()
    const observer = new MutationObserver(updateDarkMode)
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })

    // 延迟执行以确保认证状态已初始化
    nextTick(async () => {
      if (route.query.user1 && route.query.user2) {
        // 重置加载状态
        loading.value = true
        isLoadingJson.value = false
        pkData.value = null

        // 使用可复用函数获取数据
        await fetchComparisonData(route.query.user1 as string, route.query.user2 as string)
      }
    })
  })

  // 处理左侧搜索框的比较请求（左侧用户固定，用户输入右侧用户）
  const handleLeftCompare = async (query?: string) => {
    const newUser = query || searchInputRef1.value?.searchValue

    if (!newUser?.trim() || !pkData.value?.user1?.name) return

    const user1Name = pkData.value.user1.name

    // 更新路由参数：左侧用户保持不变，右侧用户使用用户输入
    router.replace({
      path: '/github/compare',
      query: {
        user1: user1Name,
        user2: newUser.trim(),
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false
    pkData.value = null

    // 使用可复用函数获取数据
    await fetchComparisonData(user1Name, newUser.trim())
  }

  // 处理右侧搜索框的比较请求（右侧用户固定，用户输入左侧用户）
  const handleRightCompare = async (query?: string) => {
    const newUser = query || searchInputRef2.value?.searchValue

    if (!newUser?.trim() || !pkData.value?.user2?.name) return

    const user2Name = pkData.value.user2.name

    // 更新路由参数：左侧用户使用用户输入，右侧用户保持不变
    router.replace({
      path: '/github/compare',
      query: {
        user1: newUser.trim(),
        user2: user2Name,
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false
    pkData.value = null

    // 使用可复用函数获取数据
    await fetchComparisonData(newUser.trim(), user2Name)
  }

  // 处理底部搜索框的比较请求
  const handleCompare = async () => {
    if (!user1Input.value || !user2Input.value) return

    // 更新路由参数
    router.replace({
      path: '/github/compare',
      query: {
        user1: user1Input.value,
        user2: user2Input.value,
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false
    pkData.value = null

    // 使用可复用函数获取数据
    await fetchComparisonData(user1Input.value, user2Input.value)
  }

  // 激活码相关函数
  const handleSubmitActivationCode = async (code: string) => {
    inviteLoading.value = true
    inviteError.value = ''

    try {
      const result = await submitActivationCode(
        '/api/activation-code/verify',
        { code },
        { headers: { userid: currentUser.value?.uid } }
      )

      if (result.data.success) {
        inviteSuccess.value = true
        showInviteModal.value = false
      } else {
        inviteError.value = 'Invalid activation code'
      }
    } catch (error) {
      inviteError.value = 'Failed to verify activation code'
    } finally {
      inviteLoading.value = false
    }
  }

  const onShowWaitingListModal = () => {
    showInviteModal.value = false
    showWaitingListModal.value = true
  }

  const onBackToInviteCode = () => {
    showWaitingListModal.value = false
    showInviteModal.value = true
  }

  const goHome = () => {
    inviteSuccess.value = false
    router.push('/')
  }



  // 监听限制信息
  watch(limitInfo, data => {
    if (data && data.errorType === 'limit') {
      showInviteModal.value = true
      inviteError.value = ''
      inviteLoading.value = false
    }
  })

  // Remove reportDataInfo watcher since we handle direct HTTP responses now
  // This was only needed for SSE-based endpoints that return JSON URLs

  // 监听 pkData 变化，确保加载状态正确
  watch(pkData, (newData) => {
    if (newData) {
      // 如果直接收到 pkData，确保加载状态被正确设置
      loading.value = false
      isLoadingJson.value = false
    }
  })

  // 模拟加载文本的函数
  const simulateLoadingText = (user1: string, user2: string) => {
    thinking.value = []

    const loadingSteps = [
      `Analyzing ${user1}'s GitHub profile...`,
      `Fetching ${user1}'s repositories and contributions...`,
      `Analyzing ${user2}'s GitHub profile...`,
      `Fetching ${user2}'s repositories and contributions...`,
      'Comparing coding patterns and project complexity...',
      'Analyzing contribution frequency and consistency...',
      'Evaluating repository impact and collaboration...',
      'Generating comprehensive comparison report...'
    ]

    let currentStep = 0

    // 添加第一步
    thinking.value.push(loadingSteps[currentStep])
    currentStep++

    // 每1.5秒添加一个新步骤
    const interval = setInterval(() => {
      if (currentStep < loadingSteps.length && loading.value) {
        thinking.value.push(loadingSteps[currentStep])
        currentStep++
      } else {
        clearInterval(interval)
      }
    }, 1500)

    // 清理函数
    return () => clearInterval(interval)
  }

  // 可复用的 API 调用函数
  const fetchComparisonData = async (user1: string, user2: string) => {
    // 清理之前的模拟（如果存在）
    if (currentSimulationCleanup) {
      currentSimulationCleanup()
    }

    // 开始模拟加载文本
    currentSimulationCleanup = simulateLoadingText(user1, user2)

    try {
      const runtimeConfig = useRuntimeConfig()
      const apiBaseUrl = runtimeConfig.public.apiBase || 'https://api.dinq.io'
      const response = await fetch(`${apiBaseUrl}/api/github/compare`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Userid': currentUser.value?.uid || '',
        },
        body: JSON.stringify({ user1, user2 }),
      })

      const contentType = response.headers.get('content-type')

      if (contentType && contentType.includes('application/json')) {
        const data = await response.json()

        if (data.success && data.data && data.data.user1 && data.data.user2) {
          pkData.value = data.data
          loading.value = false
          isLoadingJson.value = false

          // 更新 SEO 元数据
          updateCompareSeoMeta(data.data)

          // 停止模拟文本
          if (currentSimulationCleanup) {
            currentSimulationCleanup()
            currentSimulationCleanup = null
          }
          return true // Success
        } else {
          throw new Error('Invalid response format')
        }
      } else {
        // Not JSON, fall back to SSE
        response.body?.cancel()
        // 停止模拟文本，SSE会提供真实的thinking数据
        if (currentSimulationCleanup) {
          currentSimulationCleanup()
          currentSimulationCleanup = null
        }
        connectWithObj({ user1, user2 }, '/api/github/compare', { Userid: currentUser.value?.uid || '' })
        return true // SSE fallback initiated
      }
    } catch (error) {
      console.error('Error fetching comparison data:', error)
      // 停止模拟文本
      if (currentSimulationCleanup) {
        currentSimulationCleanup()
        currentSimulationCleanup = null
      }
      // Fall back to SSE on error
      connectWithObj({ user1, user2 }, '/api/github/compare', { Userid: currentUser.value?.uid || '' })
      return false
    }
  }

  // Remove fetchReportData function since we handle direct HTTP responses now
  // This was only needed for SSE-based endpoints that return JSON URLs

  // 监听路由变化 - 只在客户端挂载后执行
  watch(() => route.query, async (newQuery) => {
    if (isMounted.value && newQuery.user1 && newQuery.user2) {
      // 重置加载状态
      loading.value = true
      isLoadingJson.value = false
      pkData.value = null

      // 使用可复用函数获取数据
      await fetchComparisonData(newQuery.user1 as string, newQuery.user2 as string)
    }
  })

  // 监听路由变化，重置状态 - 只在客户端执行
  watch(
    route,
    () => {
      if (isMounted.value && (!route.query.user1 || !route.query.user2)) {
        pkData.value = null
        loading.value = false
        isLoadingJson.value = false
      }
    },
    { immediate: false } // 改为 false 避免 SSR 时立即执行
  )

  onUnmounted(() => {
    if (progressInterval) {
      clearInterval(progressInterval)
    }
    // 清理模拟加载文本
    if (currentSimulationCleanup) {
      currentSimulationCleanup()
      currentSimulationCleanup = null
    }
  })
</script>

<style scoped>
  .compare-page-bg {
    background-image: url('~/assets/image/bgimg.png');
  }

  .dark .compare-page-bg {
    background-image: url('~/assets/image/bgimgdark_1.png');
  }

  .custom-input {
    transition: all 0.3s ease;
  }

  /* 自定义脉冲动画，与学者比较页面保持一致 */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  .custom-input:focus-within {
    box-shadow: 0 0 0 3px rgba(203, 124, 93, 0.1);
  }



  .text-primary-100 {
    color: #CB7C5D;
  }

  .text-primary-200 {
    color: #B86B4F;
  }

  .bg-black-100 {
    background-color: #000000;
  }

  .sty_f_r_end {
    display: flex;
    justify-content: flex-end;
  }

  .f-cer {
    display: flex;
    align-items: center;
  }

  .fx-cer {
    display: flex;
    align-items: center;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .grid-cols-2 {
      grid-template-columns: 1fr;
    }

    .gap-7\.5 {
      gap: 1rem;
    }

    .px-30 {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .w-120 {
      width: 100%;
    }

    .w-\[280px\] {
      width: 100%;
    }
  }
</style>
