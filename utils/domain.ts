/**
 * 获取当前域名
 * 在服务端渲染时使用默认域名，在客户端使用实际域名
 */
export function getCurrentDomain(): string {
  // 在客户端，使用实际的域名
  if (process.client && typeof window !== 'undefined') {
    return window.location.origin
  }
  
  // 在服务端，尝试从请求头获取域名
  if (process.server) {
    try {
      const event = useRequestEvent()
      if (event?.node?.req?.headers?.host) {
        const protocol = event.node.req.headers['x-forwarded-proto'] || 'https'
        return `${protocol}://${event.node.req.headers.host}`
      }
    } catch (error) {
      // 如果无法获取请求信息，使用默认域名
    }
  }
  
  // 默认域名
  return 'https://dinq.io'
}

/**
 * 获取OG图片URL
 */
export function getOgImageUrl(path: string): string {
  const domain = getCurrentDomain()
  return `${domain}${path.startsWith('/') ? path : '/' + path}`
}

/**
 * 获取页面URL
 */
export function getPageUrl(path: string): string {
  const domain = getCurrentDomain()
  return `${domain}${path.startsWith('/') ? path : '/' + path}`
}
