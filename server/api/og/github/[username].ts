export default defineEventHandler(async (event) => {
  const username = getRouterParam(event, 'username')

  console.log('🔍 OG Image API called for username:', username)

  if (!username) {
    console.error('❌ No username provided')
    throw createError({
      statusCode: 400,
      statusMessage: 'Username is required'
    })
  }

  try {
    console.log('📊 Fetching GitHub data for:', username)
    // 获取GitHub用户数据
    const githubData = await fetchGitHubData(username)

    if (!githubData) {
      console.error('❌ GitHub data not found for:', username)
      throw createError({
        statusCode: 404,
        statusMessage: 'GitHub user not found'
      })
    }

    console.log('✅ GitHub data fetched successfully')

    // 构建查询参数
    const queryParams = new URLSearchParams({
      // 传递用户数据作为查询参数
      userName: githubData.user.name || githubData.user.login,
      userAvatar: githubData.user.avatarUrl,
      userLogin: githubData.user.login,
      userBio: githubData.user.bio || '',
      userRole: githubData.description || 'Developer',

      // 统计数据
      repositories: String(githubData.overview?.repositories || 0),
      stars: String(githubData.overview?.stars || 0),
      pullRequests: String(githubData.overview?.pull_requests || 0),
      additions: String(githubData.overview?.additions || 0),
      deletions: String(githubData.overview?.deletions || 0),
      workExperience: String(githubData.overview?.work_experience || 0),

      // 收入估算
      income: String(githubData.valuation_and_level?.salary_range ?
        (Array.isArray(githubData.valuation_and_level.salary_range) ?
          githubData.valuation_and_level.salary_range[0] :
          parseInt(githubData.valuation_and_level.salary_range.split(' - ')[0].replace(/[$,]/g, ''))) || 220000 : 220000),

      // 角色模型
      roleModelName: githubData.role_model?.name || '',
      roleModelAvatar: githubData.role_model?.github ?
        `https://github.com/${githubData.role_model.github.split('/').pop()}.png` : '',
      roleModelTitle: githubData.role_model?.title || '',
      roleModelAchievement: githubData.role_model?.achievement || '',

      // 编程语言（只传递前5个）
      languages: githubData.code_contribution?.languages ?
        JSON.stringify(Object.fromEntries(
          Object.entries(githubData.code_contribution.languages)
            .sort(([,a], [,b]) => (b as number) - (a as number))
            .slice(0, 5)
        )) : '{}',
      languageTotal: String(githubData.code_contribution?.total || 0),

      // 特色项目
      featureProjectName: githubData.feature_project?.name || '',
      featureProjectDescription: githubData.feature_project?.description || '',
      featureProjectStars: String(githubData.feature_project?.stargazerCount || 0),
      featureProjectForks: String(githubData.feature_project?.forkCount || 0),
      featureProjectUrl: githubData.feature_project?.url || '',

      // 最有价值PR
      mostValuablePRTitle: githubData.most_valuable_pr?.title || '',
      mostValuablePRImpact: githubData.most_valuable_pr?.impact || '',
      mostValuablePRRepository: githubData.most_valuable_pr?.repository || '',
      mostValuablePRUrl: githubData.most_valuable_pr?.url || '',

      // 薪资范围
      salaryRange: githubData.valuation_and_level?.salary_range ?
        (Array.isArray(githubData.valuation_and_level.salary_range) ?
          `$${githubData.valuation_and_level.salary_range[0]} - $${githubData.valuation_and_level.salary_range[1]}` :
          githubData.valuation_and_level.salary_range) : '',
    })

    console.log('🔗 Building query parameters...')

    // 使用nuxt-og-image生成图片
    const ogImageUrl = `/__og-image__/image/og/github-analysis/og.png?${queryParams.toString()}`

    console.log('🖼️ OG Image URL:', ogImageUrl)

    // 设置响应头
    setHeader(event, 'Content-Type', 'image/png')
    setHeader(event, 'Cache-Control', 'public, max-age=3600')

    console.log('↩️ Redirecting to OG image...')
    // 重定向到OG图片
    return sendRedirect(event, ogImageUrl, 302)
  } catch (error) {
    console.error('❌ Error generating OG image:', error)
    console.error('❌ Error stack:', error.stack)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to generate OG image: ' + error.message
    })
  }
})

// 获取GitHub数据的函数（复用现有的API逻辑）
async function fetchGitHubData(username: string) {
  try {
    // 为了测试，先返回模拟数据
    // 在生产环境中，这里应该调用你的GitHub分析API
    return {
      user: {
        name: username,
        login: username,
        avatarUrl: `https://github.com/${username}.png`,
        bio: 'Software Developer passionate about open source',
      },
      description: 'Full Stack Developer',
      overview: {
        repositories: 42,
        stars: 1337,
        pull_requests: 256,
        additions: 50000,
        deletions: 25000,
        work_experience: 5,
        issues: 128,
      },
      valuation_and_level: {
        salary_range: [180000, 220000]
      },
      role_model: {
        name: 'Linus Torvalds',
        github: 'https://github.com/torvalds',
        title: 'Creator of Linux',
        achievement: 'Changed the world with open source'
      },
      code_contribution: {
        languages: {
          'JavaScript': 3500,
          'TypeScript': 2800,
          'Python': 2100,
          'Go': 1200,
          'Rust': 800
        },
        total: 10400
      },
      feature_project: {
        name: 'awesome-project',
        description: 'A high-performance web application built with modern technologies',
        stargazerCount: 2847,
        forkCount: 456,
        url: `https://github.com/${username}/awesome-project`
      },
      most_valuable_pr: {
        title: 'Implement advanced caching mechanism for 50% performance boost',
        impact: 'Reduced API response time by 50% and improved user experience significantly',
        repository: 'microsoft/vscode',
        url: `https://github.com/microsoft/vscode/pull/12345`
      }
    }
  } catch (error) {
    console.error('Error fetching GitHub data:', error)
    return null
  }
}
