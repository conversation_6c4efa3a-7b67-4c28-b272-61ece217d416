export default defineEventHandler(async (event) => {
  const username = getRouterParam(event, 'username')
  
  if (!username) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Username is required'
    })
  }

  try {
    // 获取GitHub用户数据
    const githubData = await fetchGitHubData(username)
    
    if (!githubData) {
      throw createError({
        statusCode: 404,
        statusMessage: 'GitHub user not found'
      })
    }

    // 构建查询参数
    const queryParams = new URLSearchParams({
      // 传递用户数据作为查询参数
      userName: githubData.user.name || githubData.user.login,
      userAvatar: githubData.user.avatarUrl,
      userLogin: githubData.user.login,
      userBio: githubData.user.bio || '',
      userRole: githubData.description || 'Developer',
      
      // 统计数据
      repositories: String(githubData.overview?.repositories || 0),
      stars: String(githubData.overview?.stars || 0),
      pullRequests: String(githubData.overview?.pull_requests || 0),
      additions: String(githubData.overview?.additions || 0),
      deletions: String(githubData.overview?.deletions || 0),
      workExperience: String(githubData.overview?.work_experience || 0),
      
      // 收入估算
      income: String(githubData.valuation_and_level?.salary_range ? 
        (Array.isArray(githubData.valuation_and_level.salary_range) ? 
          githubData.valuation_and_level.salary_range[0] : 
          parseInt(githubData.valuation_and_level.salary_range.split(' - ')[0].replace(/[$,]/g, ''))) || 220000 : 220000),
      
      // 角色模型
      roleModelName: githubData.role_model?.name || '',
      roleModelAvatar: githubData.role_model?.github ? 
        `https://github.com/${githubData.role_model.github.split('/').pop()}.png` : '',
      roleModelTitle: githubData.role_model?.title || '',
      roleModelAchievement: githubData.role_model?.achievement || '',
      
      // 编程语言（只传递前5个）
      languages: githubData.code_contribution?.languages ? 
        JSON.stringify(Object.fromEntries(
          Object.entries(githubData.code_contribution.languages)
            .sort(([,a], [,b]) => (b as number) - (a as number))
            .slice(0, 5)
        )) : '{}',
      languageTotal: String(githubData.code_contribution?.total || 0),
    })

    // 重定向到nuxt-og-image生成的图片
    return sendRedirect(event, `/__og-image__/image/og/github-analysis/og.png?${queryParams.toString()}`, 302)
  } catch (error) {
    console.error('Error generating OG image:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to generate OG image'
    })
  }
})

// 获取GitHub数据的函数（复用现有的API逻辑）
async function fetchGitHubData(username: string) {
  try {
    // 为了测试，先返回模拟数据
    // 在生产环境中，这里应该调用你的GitHub分析API
    return {
      user: {
        name: username,
        login: username,
        avatarUrl: `https://github.com/${username}.png`,
        bio: 'Software Developer',
      },
      description: 'Full Stack Developer',
      overview: {
        repositories: 42,
        stars: 1337,
        pull_requests: 256,
        additions: 50000,
        deletions: 25000,
        work_experience: 5,
      },
      valuation_and_level: {
        salary_range: [180000, 220000]
      },
      role_model: {
        name: 'Linus Torvalds',
        github: 'https://github.com/torvalds',
        title: 'Creator of Linux',
        achievement: 'Changed the world with open source'
      },
      code_contribution: {
        languages: {
          'JavaScript': 3500,
          'TypeScript': 2800,
          'Python': 2100,
          'Go': 1200,
          'Rust': 800
        },
        total: 10400
      }
    }
  } catch (error) {
    console.error('Error fetching GitHub data:', error)
    return null
  }
}
